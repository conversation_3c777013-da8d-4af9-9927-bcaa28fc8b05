package data

import (
	"context"
	"time"

	entsql "entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/afpaccount"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/cooperationsiteurlchannel"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/site"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/siteadformat"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/sitecountryadformatpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/date"
)

type siteAdFormatRepo struct {
	data                  *Data
	log                   *log.Helper
	deploymentHistoryRepo biz.DeploymentHistoryRepo
	redu                  float64
}

// ListBusinessSiteData 获取商户站点数据，包含收益、点击率、RPM等关键指标
// 该函数通过聚合站点广告格式数据，计算各种业务指标用于商户平台展示
// 集成网站广告显示状态数据，根据当前状态过滤广告格式
// 处理AFP账户关系：当网站是子账号时，优先使用子账号数据而不是主账号数据
func (r *siteAdFormatRepo) ListBusinessSiteData(ctx context.Context, start, end time.Time) ([]*biz.BusinessSiteData, error) {
	r.log.WithContext(ctx).Infof("ListBusinessSiteData: start=%v, end=%v", start, end)

	// 1. 获取所有相关域名
	domains, err := r.extractDomainsFromTimeRange(ctx, start, end)
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to extract domains: %v", err)
		return nil, err
	}

	// 2. 获取网站广告显示状态数据
	deploymentHistory, err := r.deploymentHistoryRepo.GetSiteDeploymentHistory(ctx, domains)
	if err != nil {
		return nil, errors.Errorf("Failed to get ad display status: %v", err)
	}

	r.log.WithContext(ctx).Infof("Got ad display status for %d domains, %d records found",
		len(domains), len(deploymentHistory))

	// 3. 获取AFP账户关系映射
	afpAccountMap, err := r.getAFPAccountMapping(ctx, domains)
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to get AFP account mapping: %v", err)
		return nil, err
	}

	r.log.WithContext(ctx).Infof("Got AFP account mapping for %d sites", len(afpAccountMap))

	// 4. 构建查询
	query := r.data.db.SiteAdFormat.Query()

	// 构建日期范围过滤条件
	datePredicates := r.buildDateRangePredicates(start, end)

	// 构建聚合字段列表
	aggregationFields := r.buildAggregationFields()

	var all []*biz.BusinessSiteData
	err = query.Modify(func(s *entsql.Selector) {
		// 应用日期范围过滤条件
		r.applyDateRangeFilters(s, datePredicates)

		// 构建表连接
		t1, t2, t4 := r.buildTableReferences()

		aggregationFields = append(aggregationFields, "max(t2.page_views) AS page_views")

		// 构建JOIN条件和WHERE条件，包含广告格式过滤和AFP账户过滤
		r.buildJoinAndWhereConditionsWithAdFormatAndAFPFilter(s, t1, t2, t4, aggregationFields, deploymentHistory, afpAccountMap, start, end)
	}).Scan(ctx, &all)

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to execute query: %v", err)
		return nil, err
	}

	r.log.WithContext(ctx).Infof("ListBusinessSiteData completed, returned %d records", len(all))
	return all, nil
}

// getAFPAccountMapping 获取AFP账户关系映射
// 返回map[site]map[account]child_account，用于确定每个网站的账户关系
func (r *siteAdFormatRepo) getAFPAccountMapping(ctx context.Context, domains []string) (map[string]string, error) {
	if len(domains) == 0 {
		return make(map[string]string), nil
	}

	// 查询AFP账户关系
	afpAccounts, err := r.data.db.AFPAccount.Query().
		Where(afpaccount.SiteIn(domains...)).
		All(ctx)
	if err != nil {
		return nil, err
	}

	// 构建映射：site -> child_account
	// 由于数据库结构保证一个网站只对应一个AFP关系，直接映射到子账号
	afpMap := make(map[string]string)
	for _, afp := range afpAccounts {
		if existingChild, exists := afpMap[afp.Site]; exists {
			r.log.Warnf("网站 %s 存在重复的AFP关系，已有子账号: %s，新子账号: %s，将使用新的",
				afp.Site, existingChild, afp.ChildAccount)
		}
		afpMap[afp.Site] = afp.ChildAccount
	}

	return afpMap, nil
}

// buildDateRangePredicates 构建日期范围过滤谓词
// 确保时区一致性：将输入时间转换为UTC时区进行比较
func (r *siteAdFormatRepo) buildDateRangePredicates(start, end time.Time) []*entsql.Predicate {
	// 将时间转换为UTC时区，确保与数据库中的时间格式一致
	startUTC := start.UTC()
	endUTC := end.UTC()

	return []*entsql.Predicate{
		entsql.GTE(entsql.Table(siteadformat.Table).C(sitecountryadformatpartition.FieldDate), startUTC),
		entsql.LTE(entsql.Table(siteadformat.Table).C(sitecountryadformatpartition.FieldDate), endUTC),
	}
}

// buildAggregationFields 构建聚合字段列表，包含基础字段和计算字段
func (r *siteAdFormatRepo) buildAggregationFields() []string {
	var agg []string

	// 1. 基础聚合字段：对基础指标进行求和
	for _, field := range AggField {
		if field == "page_views" {
			continue
		}
		agg = append(agg, entsql.As(entsql.Sum(entsql.Table(siteadformat.Table).C(field)), field))
	}

	// 2. 收益相关计算字段
	agg = append(agg, r.buildEarningsFields()...)

	// 3. 点击率和覆盖率计算字段
	agg = append(agg, r.buildRateFields()...)

	// 4. RPM（千次展示收益）计算字段
	agg = append(agg, r.buildRPMFields()...)

	// 5. 其他业务指标字段
	agg = append(agg, r.buildOtherMetricFields()...)

	// 6. 分组维度字段
	agg = append(agg, r.buildDimensionFields()...)

	return agg
}

// buildEarningsFields 构建收益相关的计算字段
func (r *siteAdFormatRepo) buildEarningsFields() []string {
	return []string{
		// 预估收益：四舍五入到2位小数
		entsql.As("CAST(ROUND(SUM(site_ad_formats.estimated_earnings)::numeric, 2) AS numeric(20, 2))", "estimated_earnings"),
		// 每次点击费用：收益除以点击数（优化：使用CASE WHEN替代COALESCE/NULLIF）
		entsql.As("CASE WHEN SUM(site_ad_formats.clicks) > 0 THEN ROUND((SUM(site_ad_formats.estimated_earnings) / SUM(site_ad_formats.clicks))::numeric, 4) ELSE 0 END", "cost_per_click"),
	}
}

// buildRateFields 构建点击率和覆盖率相关的计算字段
func (r *siteAdFormatRepo) buildRateFields() []string {
	return []string{
		// 展示点击率：点击数除以展示数（优化：使用CASE WHEN提升性能）
		entsql.As("CASE WHEN SUM(site_ad_formats.impressions) > 0 THEN ROUND((SUM(site_ad_formats.clicks)::numeric / SUM(site_ad_formats.impressions))::numeric, 4) ELSE 0 END", "impressions_ctr"),
		// 广告请求覆盖率：匹配的广告请求数除以总广告请求数
		entsql.As("CASE WHEN SUM(site_ad_formats.ad_requests) > 0 THEN ROUND((SUM(site_ad_formats.matched_ad_requests)::numeric / SUM(site_ad_formats.ad_requests))::numeric, 4) ELSE 0 END", "ad_requests_coverage"),
	}
}

// buildRPMFields 构建RPM（千次展示收益）相关的计算字段
func (r *siteAdFormatRepo) buildRPMFields() []string {
	return []string{
		// 展示RPM：每千次展示的收益（优化：简化计算逻辑）
		entsql.As("CASE WHEN SUM(site_ad_formats.impressions) > 0 THEN ROUND((SUM(site_ad_formats.estimated_earnings) / SUM(site_ad_formats.impressions) * 1000)::numeric, 4) ELSE 0 END", "impressions_rpm"),
		// 页面浏览RPM：每千次页面浏览的收益
		entsql.As("CASE WHEN MAX(t2.page_views) > 0 THEN ROUND((SUM(site_ad_formats.estimated_earnings) / MAX(t2.page_views) * 1000)::numeric, 4) ELSE 0 END", "page_views_rpm"),
	}
}

// buildOtherMetricFields 构建其他业务指标字段
func (r *siteAdFormatRepo) buildOtherMetricFields() []string {
	return []string{
		// 可见度：sum(impressions*active_view_viewability)/sum(impressions)
		entsql.As("CASE WHEN SUM(site_ad_formats.impressions) > 0 THEN SUM(site_ad_formats.impressions*site_ad_formats.active_view_viewability)/SUM(site_ad_formats.impressions) ELSE 0 END", "active_view_viewability"),

		// 每页面浏览展示数：展示数除以页面浏览数（优化：简化计算）
		entsql.As("CASE WHEN MAX(t2.page_views) > 0 THEN ROUND((SUM(site_ad_formats.impressions)::numeric / MAX(t2.page_views))::numeric, 4) ELSE 0 END", "impressions_per_page_view"),
	}
}

// buildDimensionFields 构建分组维度字段
func (r *siteAdFormatRepo) buildDimensionFields() []string {
	return []string{
		entsql.Table(siteadformat.Table).C(siteadformat.FieldSite),
		entsql.Table(siteadformat.Table).C(siteadformat.FieldDate),
	}
}

// applyDateRangeFilters 应用日期范围过滤条件
func (r *siteAdFormatRepo) applyDateRangeFilters(s *entsql.Selector, predicates []*entsql.Predicate) {
	for _, p := range predicates {
		s = s.Where(p)
	}
}

// buildTableReferences 构建表引用
func (r *siteAdFormatRepo) buildTableReferences() (*entsql.SelectTable, *entsql.SelectTable, *entsql.SelectTable) {
	t1 := entsql.Table(cooperationsiteurlchannel.Table)
	t2 := entsql.Table(siteadformat.Table)
	t4 := entsql.Table(site.Table)
	return t1, t2, t4
}

// applyBusinessLogicFilters 应用业务逻辑过滤条件
func (r *siteAdFormatRepo) applyBusinessLogicFilters(s *entsql.Selector, t1 *entsql.SelectTable) {
	// 优化：调整WHERE条件顺序，将选择性高的条件放在前面
	s.Where(
		// 1. url_channel 为空的才是网站（选择性高，先过滤）
		entsql.EQ(cooperationsiteurlchannel.FieldURLChannel, ""),
	).Where(
		// 2. 过滤未删除的记录
		entsql.IsNull(t1.C(cooperationsiteurlchannel.FieldDeletedAt)),
	).Where(
		// 3. 过滤停止时间条件：要么没有停止时间，要么数据日期在停止时间之前
		entsql.Or(
			entsql.EQ(t1.C(cooperationsiteurlchannel.FieldStoppingTime), time.Time{}),
			entsql.LT(s.C(sitecountryadformatpartition.FieldDate), entsql.Column("stopping_time")),
		),
	)
}

func (r *siteAdFormatRepo) GetSiteEstimatedEarnings(ctx context.Context, start time.Time, end time.Time) ([]*biz.AdsenseSiteAdFormatData, error) {
	ps := []*entsql.Predicate{entsql.GTE(siteadformat.FieldDate, start), entsql.LTE(siteadformat.FieldDate, end)}

	var all []*biz.AdsenseSiteAdFormatData
	query := r.data.db.SiteAdFormat.Query()

	err := query.Modify(func(s *entsql.Selector) {
		for _, p := range ps {
			s = s.Where(p)
		}
		t1 := entsql.Table(cooperationsiteurlchannel.Table)
		t2 := entsql.Table(siteadformat.Table)
		where := s.LeftJoin(t1).On(t1.C(cooperationsiteurlchannel.FieldSite), t2.C(siteadformat.FieldSite)).
			Where(entsql.IsNull(cooperationsiteurlchannel.FieldDeletedAt))
		where.
			Where(entsql.EQ(cooperationsiteurlchannel.FieldURLChannel, "")).
			Where(entsql.NEQ(siteadformat.FieldAdFormat, "GIGA")).
			Select(entsql.Table(siteadformat.Table).C(siteadformat.FieldSite),
				cooperationsiteurlchannel.FieldCooperationChannel,
				entsql.As(entsql.Sum(siteadformat.FieldEstimatedEarnings), siteadformat.FieldEstimatedEarnings)).
			GroupBy(entsql.Table(siteadformat.Table).C(siteadformat.FieldSite), cooperationsiteurlchannel.FieldCooperationChannel).
			OrderBy(cooperationsiteurlchannel.FieldCooperationChannel, entsql.Desc(siteadformat.FieldEstimatedEarnings))

	}).
		Scan(ctx, &all)
	if err != nil {
		return nil, err
	}

	return all, nil
}

func (r *siteAdFormatRepo) CreateHistory(ctx context.Context, datas []*biz.AdsenseSiteAdFormatData) error {
	bulk := make([]*ent.SiteAdFormatHistoryCreate, 0)

	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	cstSh, _ := time.LoadLocation("Asia/Shanghai")
	nowDate := time.Now().In(cstSh)
	now := time.Date(nowDate.Year(), nowDate.Month(), nowDate.Day(), 0, 0, 0, 0, cstSh)
	for i, data := range datas {
		bulk = append(bulk, tx.SiteAdFormatHistory.Create().SetDate(data.Date).SetCollectedAt(now).SetSite(data.Site).
			SetAdFormat(data.AdFormat).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick).SetActiveViewTime(data.ActiveViewTime).
			SetAccount(data.Account).SetPlatform(data.Platform))

		if (i+1)%2000 == 0 || i == len(datas)-1 {
			err = tx.SiteAdFormatHistory.CreateBulk(bulk...).OnConflict(entsql.ConflictColumns("collected_at", "date", "site", "ad_format", "account", "platform")).UpdateNewValues().Exec(ctx)
			// _, err := tx.SiteAdFormat.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}

			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func (r *siteAdFormatRepo) CreateHKD(ctx context.Context, datas []*biz.AdsenseSiteAdFormatData) error {
	bulk := make([]*ent.SiteAdFormatHKDCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}

	for i, data := range datas {
		bulk = append(bulk, tx.SiteAdFormatHKD.Create().SetDate(data.Date).SetSite(data.Site).
			SetAdFormat(data.AdFormat).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick).SetActiveViewTime(data.ActiveViewTime).
			SetAccount(data.Account).SetPlatform(data.Platform))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err = tx.SiteAdFormatHKD.CreateBulk(bulk...).OnConflict(entsql.ConflictColumns("date", "site", "ad_format", "account", "platform")).UpdateNewValues().Exec(ctx)

			// _, err := tx.SiteAdFormat.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func (r *siteAdFormatRepo) Create(ctx context.Context, datas []*biz.AdsenseSiteAdFormatData, hasSaveHistory bool) error {
	bulk := make([]*ent.SiteAdFormatCreate, 0)
	siteAdFormatHistoryBulk := make([]*ent.SiteAdFormatHistoryCreate, 0)
	var now time.Time
	if hasSaveHistory {
		cstSh, _ := time.LoadLocation("Asia/Shanghai")
		nowDate := time.Now().In(cstSh)
		now = time.Date(nowDate.Year(), nowDate.Month(), nowDate.Day(), 0, 0, 0, 0, cstSh)
	}
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	//s, e := date.GetDateRange("LAST_7_DAYS", time.Now())
	//_, err = tx.SiteAdFormat.Delete().Where(siteadformat.DateGTE(s), siteadformat.DateLTE(e)).Exec(ctx)
	//if err != nil {
	//	return rollback(tx, err)
	//}
	for i, data := range datas {
		bulk = append(bulk, tx.SiteAdFormat.Create().SetDate(data.Date).SetSite(data.Site).
			SetAdFormat(data.AdFormat).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick).SetActiveViewTime(data.ActiveViewTime).
			SetAccount(data.Account).SetPlatform(data.Platform))
		if hasSaveHistory {
			siteAdFormatHistoryBulk = append(siteAdFormatHistoryBulk, tx.SiteAdFormatHistory.Create().SetCollectedAt(now).SetDate(data.Date).SetSite(data.Site).
				SetAdFormat(data.AdFormat).SetEstimatedEarnings(data.EstimatedEarnings).
				SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
				SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
				SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
				SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
				SetActiveViewViewability(data.ActiveViewViewability).
				SetMatchedAdRequests(data.MatchedAdRequests).
				SetCostPerClick(data.CostPerClick).SetActiveViewTime(data.ActiveViewTime).
				SetAccount(data.Account).SetPlatform(data.Platform))
		}
		if (i+1)%2000 == 0 || i == len(datas)-1 {
			err = tx.SiteAdFormat.CreateBulk(bulk...).OnConflict(entsql.ConflictColumns("date", "site", "ad_format", "account", "platform")).UpdateNewValues().Exec(ctx)
			// _, err := tx.SiteAdFormat.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			if hasSaveHistory {
				err = tx.SiteAdFormatHistory.CreateBulk(siteAdFormatHistoryBulk...).OnConflict(entsql.ConflictColumns("collected_at", "date", "site", "ad_format", "account", "platform")).UpdateNewValues().Exec(ctx)
				if err != nil {
					return rollback(tx, err)
				}
				siteAdFormatHistoryBulk = siteAdFormatHistoryBulk[:0]
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

// GetMaxSiteEstimatedEarnings 获取指定时间范围内每个站点每天的最大收益汇总
func (r *siteAdFormatRepo) GetMaxSiteEstimatedEarnings(ctx context.Context, start time.Time, end time.Time) ([]*biz.SiteEarningsResult, error) {
	// 构建复杂的 SQL 查询，等价于原始 SQL
	query := `
		SELECT
		    site,
		    ROUND(SUM(best_daily_earnings)::numeric, 2) as total_earnings
		FROM (
		    SELECT DISTINCT
		        site,
		        "date",
		        MAX(SUM(estimated_earnings)) OVER (PARTITION BY site, "date") as best_daily_earnings
		    FROM "public"."site_ad_format_histories"
		    WHERE "date" >= $1
		      AND "date" <= $2
		    GROUP BY site, "date", collected_at
		) ranked_data
		GROUP BY site
	`

	rows, err := r.data.stddb.QueryContext(ctx, query, start, end)
	if err != nil {
		r.log.Errorf("GetMaxSiteEstimatedEarnings query error: %v", err)
		return nil, err
	}
	defer rows.Close()

	var results []*biz.SiteEarningsResult
	for rows.Next() {
		var result biz.SiteEarningsResult
		err := rows.Scan(&result.Site, &result.TotalEarnings)
		if err != nil {
			r.log.Errorf("GetMaxSiteEstimatedEarnings scan error: %v", err)
			return nil, err
		}
		results = append(results, &result)
	}

	if err = rows.Err(); err != nil {
		r.log.Errorf("GetMaxSiteEstimatedEarnings rows error: %v", err)
		return nil, err
	}

	return results, nil
}

func NewSiteAdFormatRepo(channelGaReportConf *conf.ChannelGaReport, data *Data, deploymentHistoryRepo biz.DeploymentHistoryRepo, logger log.Logger) biz.AdSenseSiteAdFormatRepo {
	return &siteAdFormatRepo{
		data:                  data,
		log:                   log.NewHelper(log.With(logger, "module", "data/site/ad_format")),
		deploymentHistoryRepo: deploymentHistoryRepo,
		redu:                  channelGaReportConf.GetRedu(),
	}
}

func (r *siteAdFormatRepo) GetSiteReduction(ctx context.Context, start, end time.Time) ([]*biz.SiteReductionResult, error) {
	// 构建复杂的 SQL 查询，等价于原始 SQL
	query := `
		WITH aggregated_by_collection AS (
			-- 第一步：先将同一个(date, site, collected_at)的所有ad_format数据汇总
			SELECT date, site, collected_at, SUM(impressions) AS total_impressions
				, SUM(clicks) AS total_clicks, SUM(ad_requests) AS total_ad_requests
				, SUM(estimated_earnings) AS total_estimated_earnings
			FROM site_ad_format_histories
			WHERE date >= $1
				AND date <= $2
			GROUP BY date, site, collected_at
		),
		latest_two_per_site AS (
			-- 第二步：为每个(date, site)找出最新的两个collected_at，并标记总数量
			SELECT *, ROW_NUMBER() OVER (PARTITION BY date, site ORDER BY collected_at DESC) AS rn
				, COUNT(*) OVER (PARTITION BY date, site ) AS total_collections
			FROM aggregated_by_collection
		),
		comparison_data AS (
			-- 第三步：将最新和次新的数据进行对比
			SELECT latest.date, latest.site, latest.collected_at AS latest_collected_at, previous.collected_at AS previous_collected_at, latest.total_impressions AS latest_impressions
				, COALESCE(previous.total_impressions, 0) AS previous_impressions, latest.total_clicks AS latest_clicks
				, COALESCE(previous.total_clicks, 0) AS previous_clicks, latest.total_ad_requests AS latest_ad_requests
				, COALESCE(previous.total_ad_requests, 0) AS previous_ad_requests, latest.total_estimated_earnings AS latest_estimated_earnings
				, COALESCE(previous.total_estimated_earnings, 0) AS previous_estimated_earnings
				, latest.total_impressions - COALESCE(previous.total_impressions, 0) AS impressions_change
				, latest.total_clicks - COALESCE(previous.total_clicks, 0) AS clicks_change
				, latest.total_ad_requests - COALESCE(previous.total_ad_requests, 0) AS ad_requests_change
				, latest.total_estimated_earnings - COALESCE(previous.total_estimated_earnings, 0) AS estimated_earnings_change
			FROM (
				(SELECT *
				FROM latest_two_per_site
				WHERE rn = 1
					AND total_collections >= 2)
			) latest
				INNER JOIN (
					SELECT *
					FROM latest_two_per_site
					WHERE rn = 2
						AND total_collections >= 2
				) previous
				ON latest.date = previous.date
					AND latest.site = previous.site
		)
		-- 最终结果：只显示有变化的记录
		SELECT date, site, latest_collected_at, previous_collected_at, latest_impressions
			, previous_impressions, impressions_change, latest_clicks, previous_clicks, clicks_change
			, latest_ad_requests, previous_ad_requests, ad_requests_change, latest_estimated_earnings, previous_estimated_earnings
			, estimated_earnings_change
			, CASE
				WHEN previous_impressions > 0 THEN ROUND(impressions_change::numeric / previous_impressions::numeric * 100, 2)
				ELSE NULL
			END AS impressions_change_percent
			, CASE
				WHEN previous_clicks > 0 THEN ROUND(clicks_change::numeric / previous_clicks::numeric * 100, 2)
				ELSE NULL
			END AS clicks_change_percent
			, CASE
				WHEN previous_ad_requests > 0 THEN ROUND(ad_requests_change::numeric / previous_ad_requests::numeric * 100, 2)
				ELSE NULL
			END AS ad_requests_change_percent
			, CASE
				WHEN previous_estimated_earnings > 0 THEN ROUND(estimated_earnings_change::numeric / previous_estimated_earnings::numeric * 100, 2)
				ELSE NULL
			END AS estimated_earnings_change_percent
		FROM comparison_data
		WHERE (previous_impressions > 0
				AND ABS(impressions_change::numeric / previous_impressions::numeric ) >= $3)
			OR (previous_clicks > 0
				AND ABS(clicks_change::numeric / previous_clicks::numeric ) >= $4)
			OR (previous_ad_requests > 0
				AND ABS(ad_requests_change::numeric / previous_ad_requests::numeric ) >= $5)
			OR (previous_estimated_earnings > 0
				AND ABS(estimated_earnings_change::numeric / previous_estimated_earnings::numeric ) >= $6)
			OR (previous_impressions = 0
				AND latest_impressions > 0)
			OR (previous_clicks = 0
				AND latest_clicks > 0)
			OR (previous_ad_requests = 0
				AND latest_ad_requests > 0)
			OR (previous_estimated_earnings = 0
				AND latest_estimated_earnings > 0)
	`

	rows, err := r.data.stddb.QueryContext(ctx, query, start, end, r.redu, r.redu, r.redu, r.redu)
	if err != nil {
		r.log.Errorf("GetSiteReduction query error: %v", err)
		return nil, err
	}
	defer rows.Close()

	var results []*biz.SiteReductionResult
	for rows.Next() {
		var result biz.SiteReductionResult
		var impressionsChangePercent, clicksChangePercent, adRequestsChangePercent, estimatedEarningsChangePercent *float64

		err := rows.Scan(
			&result.Date, &result.Site, &result.LatestCollectedAt, &result.PreviousCollectedAt,
			&result.LatestImpressions, &result.PreviousImpressions, &result.ImpressionsChange,
			&result.LatestClicks, &result.PreviousClicks, &result.ClicksChange,
			&result.LatestAdRequests, &result.PreviousAdRequests, &result.AdRequestsChange,
			&result.LatestEstimatedEarnings, &result.PreviousEstimatedEarnings, &result.EstimatedEarningsChange,
			&impressionsChangePercent, &clicksChangePercent, &adRequestsChangePercent, &estimatedEarningsChangePercent,
		)
		if err != nil {
			r.log.Errorf("GetSiteReduction scan error: %v", err)
			return nil, err
		}

		result.ImpressionsChangePercent = impressionsChangePercent
		result.ClicksChangePercent = clicksChangePercent
		result.AdRequestsChangePercent = adRequestsChangePercent
		result.EstimatedEarningsChangePercent = estimatedEarningsChangePercent

		results = append(results, &result)
	}

	if err = rows.Err(); err != nil {
		r.log.Errorf("GetSiteReduction rows error: %v", err)
		return nil, err
	}

	return results, nil
}

// extractDomainsFromTimeRange 从指定时间范围内提取所有相关域名
func (r *siteAdFormatRepo) extractDomainsFromTimeRange(ctx context.Context, start, end time.Time) ([]string, error) {
	// 查询指定时间范围内的所有站点域名
	query := `
		SELECT DISTINCT saf.site
		FROM site_ad_formats saf
		WHERE saf.date >= $1
		  AND saf.date <= $2
	`

	rows, err := r.data.stddb.QueryContext(ctx, query, start, end)
	if err != nil {
		r.log.Errorf("extractDomainsFromTimeRange query error: %v", err)
		return nil, err
	}
	defer rows.Close()

	var domains []string
	for rows.Next() {
		var domain string
		if err := rows.Scan(&domain); err != nil {
			r.log.Errorf("extractDomainsFromTimeRange scan error: %v", err)
			return nil, err
		}
		domains = append(domains, domain)
	}

	if err = rows.Err(); err != nil {
		r.log.Errorf("extractDomainsFromTimeRange rows error: %v", err)
		return nil, err
	}

	r.log.Infof("Extracted %d domains from time range %v to %v", len(domains), start, end)
	return domains, nil
}

// buildJoinAndWhereConditionsWithAdFormatFilter 构建JOIN条件和WHERE条件，包含广告格式过滤
func (r *siteAdFormatRepo) buildJoinAndWhereConditionsWithAdFormatFilter(
	s *entsql.Selector,
	t1, t2, t4 *entsql.SelectTable,
	agg []string,
	deploymentHistory map[string][]*biz.SiteDeploymentHistory,
	start, end time.Time) {

	// 构建LEFT JOIN条件
	joinedSelector := s.LeftJoin(t1).On(
		t1.C(cooperationsiteurlchannel.FieldSite),
		t2.C(siteadformat.FieldSite),
	).LeftJoin(t4).On(t4.C(site.FieldSite),
		t2.C(siteadformat.FieldSite)).On(t4.C(site.FieldDate),
		t2.C(siteadformat.FieldDate))

	// 构建基础WHERE条件
	r.applyBusinessLogicFilters(joinedSelector, t1)

	// 应用广告格式过滤条件
	r.applyAdFormatFilters(joinedSelector, deploymentHistory, start, end)

	// 设置SELECT字段和GROUP BY条件
	joinedSelector.Select(agg...).GroupBy(
		entsql.Table(siteadformat.Table).C(siteadformat.FieldDate),
		entsql.Table(siteadformat.Table).C(siteadformat.FieldSite),
	)
}

// buildJoinAndWhereConditionsWithAdFormatAndAFPFilter 构建JOIN条件和WHERE条件，包含广告格式过滤和AFP账户过滤
func (r *siteAdFormatRepo) buildJoinAndWhereConditionsWithAdFormatAndAFPFilter(
	s *entsql.Selector,
	t1, t2, t4 *entsql.SelectTable,
	agg []string,
	deploymentHistory map[string][]*biz.SiteDeploymentHistory,
	afpAccountMap map[string]string,
	start, end time.Time) {

	// 构建LEFT JOIN条件
	joinedSelector := s.LeftJoin(t1).On(
		t1.C(cooperationsiteurlchannel.FieldSite),
		t2.C(siteadformat.FieldSite),
	).LeftJoin(t4).On(t4.C(site.FieldSite),
		t2.C(siteadformat.FieldSite)).On(t4.C(site.FieldDate),
		t2.C(siteadformat.FieldDate)).On(t4.C(site.FieldAccount),
		t2.C(siteadformat.FieldAccount)).On(t4.C(site.FieldPlatform),
		t2.C(siteadformat.FieldPlatform))

	// 构建基础WHERE条件
	r.applyBusinessLogicFilters(joinedSelector, t1)

	// 应用广告格式过滤条件
	r.applyAdFormatFilters(joinedSelector, deploymentHistory, start, end)

	// 应用AFP账户过滤条件：优先使用子账号数据
	r.applyAFPAccountFilters(joinedSelector, afpAccountMap)

	// 设置SELECT字段和GROUP BY条件
	joinedSelector.Select(agg...).GroupBy(
		entsql.Table(siteadformat.Table).C(siteadformat.FieldDate),
		entsql.Table(siteadformat.Table).C(siteadformat.FieldSite),
	)
}

// applyAFPAccountFilters 应用AFP账户过滤条件
// 当网站存在AFP账户关系时，优先使用子账号数据而不是主账号数据
func (r *siteAdFormatRepo) applyAFPAccountFilters(
	s *entsql.Selector,
	afpAccountMap map[string]string) {

	if len(afpAccountMap) == 0 {
		r.log.Info("No AFP account mapping found, skipping AFP account filtering")
		return
	}

	// 构建AFP账户过滤条件
	// 对于每个有AFP关系的网站，只选择子账号的数据
	var afpConditions []*entsql.Predicate

	for site, childAccount := range afpAccountMap {
		// 对于有AFP关系的网站，只选择子账号数据
		siteCondition := entsql.And(
			entsql.EQ(entsql.Table(siteadformat.Table).C(siteadformat.FieldSite), site),
			entsql.EQ(entsql.Table(siteadformat.Table).C(siteadformat.FieldAccount), childAccount),
		)
		afpConditions = append(afpConditions, siteCondition)
	}

	if len(afpConditions) > 0 {
		// 获取所有有AFP关系的网站列表
		var afpSites []any
		for site := range afpAccountMap {
			afpSites = append(afpSites, site)
		}

		finalCondition := entsql.Or(
			entsql.Or(afpConditions...),
			entsql.NotIn(entsql.Table(siteadformat.Table).C(siteadformat.FieldSite), afpSites...), // 非AFP网站的所有数据
		)

		s.Where(finalCondition)
		r.log.Infof("Applied AFP account filtering for %d sites", len(afpAccountMap))
	}
}

// applyAdFormatFilters 应用广告格式过滤条件
// 根据网站当前广告显示状态决定是否过滤广告格式
func (r *siteAdFormatRepo) applyAdFormatFilters(
	s *entsql.Selector,
	deploymentHistory map[string][]*biz.SiteDeploymentHistory,
	start, end time.Time) {

	// 构建复杂的广告格式过滤条件
	// 基本思路：为每个域名和日期组合构建相应的过滤策略

	// 如果没有广告显示状态数据，不应用任何广告格式过滤
	if len(deploymentHistory) == 0 {
		r.log.Info("No ad display status found, skipping ad format filtering")
		return
	}

	// 构建动态的广告格式过滤条件
	r.buildDynamicAdFormatFilter(s, deploymentHistory, start, end)
}

// buildDynamicAdFormatFilter 构建动态广告格式过滤条件
// 根据域名广告显示状态决定广告格式过滤：只显示游戏内广告时仅显示游戏内广告收入，否则显示所有广告收入
func (r *siteAdFormatRepo) buildDynamicAdFormatFilter(
	s *entsql.Selector,
	deploymentHistory map[string][]*biz.SiteDeploymentHistory,
	start, end time.Time) {

	// 收集需要应用游戏内广告过滤的条件
	var inGameOnlyConditions []*entsql.Predicate

	// 遍历所有有历史记录的域名，构建相应的过滤条件
	for domain, historyList := range deploymentHistory {
		if len(historyList) == 0 {
			continue
		}

		// 根据域名广告显示状态构建过滤条件
		domainConditions := r.buildDomainSpecificAdFormatFilterByStrategy(domain, historyList, start, end)
		if len(domainConditions) > 0 {
			// 将该域名的所有条件用OR连接
			domainCondition := entsql.Or(domainConditions...)
			inGameOnlyConditions = append(inGameOnlyConditions, domainCondition)
		}
	}

	// 如果有需要过滤的条件，应用广告格式过滤
	if len(inGameOnlyConditions) > 0 {
		// 构建最终的过滤条件：
		// 1. 要么不在需要过滤的域名/日期范围内
		// 2. 要么在过滤范围内但是游戏内广告格式

		// 需要过滤的条件（域名和日期的组合）
		filterCondition := entsql.Or(inGameOnlyConditions...)
		inGameAdCondition := entsql.In(entsql.Table(siteadformat.Table).C(siteadformat.FieldAdFormat), biz.InGameAdFormats...)

		// 最终条件：要么不需要过滤，要么需要过滤但是游戏内广告
		finalCondition := entsql.Or(
			entsql.Not(filterCondition),                    // 不在过滤范围内
			entsql.And(filterCondition, inGameAdCondition), // 在过滤范围内且是游戏内广告
		)

		s.Where(finalCondition)

		r.log.Infof("Applied ad format filtering for %d domains", len(deploymentHistory))
	}
}

// buildDomainSpecificAdFormatFilterByStrategy 为特定域名构建基于广告显示状态的广告格式过滤条件
// 返回该域名需要应用游戏内广告过滤的日期条件列表
//
// 核心业务逻辑：
// - 只显示游戏内广告（状态=1）：仅显示游戏内广告收入
// - 显示所有广告（状态=0）：显示所有广告收入
// - 当前实现只返回当前状态，不支持历史状态切换
func (r *siteAdFormatRepo) buildDomainSpecificAdFormatFilterByStrategy(
	domain string,
	historyList []*biz.SiteDeploymentHistory,
	start, end time.Time) []*entsql.Predicate {

	var conditions []*entsql.Predicate

	// 域名匹配条件 - 使用表前缀避免歧义
	domainCondition := entsql.EQ(entsql.Table(siteadformat.Table).C(siteadformat.FieldSite), domain)

	// 如果没有历史记录，不应用过滤（显示所有广告数据）
	if len(historyList) == 0 {
		return conditions
	}

	// 根据广告显示状态构建过滤条件
	conditions = r.buildConditionsFromStrategyHistory(domainCondition, historyList, start, end)

	return conditions
}

// buildConditionsFromStrategyHistory 根据广告显示状态记录构建过滤条件
func (r *siteAdFormatRepo) buildConditionsFromStrategyHistory(
	domainCondition *entsql.Predicate,
	historyList []*biz.SiteDeploymentHistory,
	start, end time.Time) []*entsql.Predicate {

	var conditions []*entsql.Predicate

	// 直接使用时间段信息构建过滤条件，不再需要复杂的排序和分组逻辑
	// 因为现在每个 SiteDeploymentHistory 记录已经包含了完整的时间段信息
	timeSegments := r.buildTimeSegmentsFromHistory(historyList, start, end)

	// 为每个需要过滤的时间段构建条件
	for _, segment := range timeSegments {
		if segment.needsFiltering {
			// 只显示游戏内广告的时间段需要过滤
			var segmentCondition *entsql.Predicate

			if segment.hasEndTime {
				// 有结束时间：使用 >= 开始时间 AND < 结束时间
				segmentCondition = entsql.And(
					entsql.GTE(entsql.Table(siteadformat.Table).C(siteadformat.FieldDate), segment.startDate),
					entsql.LT(entsql.Table(siteadformat.Table).C(siteadformat.FieldDate), segment.endDate),
				)
			} else {
				// 没有结束时间：只使用 >= 开始时间
				segmentCondition = entsql.GTE(entsql.Table(siteadformat.Table).C(siteadformat.FieldDate), segment.startDate)
			}

			conditions = append(conditions, entsql.And(domainCondition, segmentCondition))
		}
	}

	return conditions
}

// buildTimeSegmentsFromHistory 根据广告显示状态记录构建时间段
// 直接使用记录中的 StartTime 和 EndTime 信息
// 应用时间调整逻辑：只有当历史记录开始时间晚于查询时间时才调整到前一天16:00
func (r *siteAdFormatRepo) buildTimeSegmentsFromHistory(
	historyList []*biz.SiteDeploymentHistory,
	start, end time.Time) []TimeSegmentByStrategy {

	var segments []TimeSegmentByStrategy

	for _, history := range historyList {
		// 只处理需要过滤的记录（只显示游戏内广告）
		if history.MonetizationStrategy != biz.MonetizationStrategyGameAds {
			continue
		}

		// 确定时间段的开始和结束时间
		segmentStart := start.UTC()
		segmentEnd := end.UTC()
		hasEndTime := true           // 默认有结束时间
		needsTimeAdjustment := false // 是否需要时间调整

		// 如果记录有开始时间，且开始时间在查询范围内或之后
		if history.StartTime != nil {
			if history.StartTime.After(start) {
				segmentStart = *history.StartTime
				needsTimeAdjustment = true // 只有当使用历史记录时间时才需要调整
			}
			// 如果历史记录开始时间早于或等于查询时间，直接使用查询时间，不调整
		}

		// 如果记录有结束时间，且结束时间在查询范围内或之前
		if history.EndTime != nil {
			if history.EndTime.Before(end) {
				segmentEnd = *history.EndTime
			}
		} else {
			// 如果记录没有结束时间，标记为没有结束时间
			hasEndTime = false
		}

		// 根据情况决定是否应用时间调整逻辑
		var timeAdjustment *date.TimeAdjustmentResult
		if needsTimeAdjustment {
			// 历史记录开始时间晚于查询时间：需要调整到前一天16:00
			timeAdjustment = date.AdjustTimeToPreviousDay(segmentStart, segmentEnd)
		} else {
			// 历史记录开始时间早于或等于查询时间：开始时间直接使用查询时间，结束时间仍需调整
			// 先获取原始的时间调整结果以获取调整后的结束时间
			originalAdjustment := date.AdjustTimeToPreviousDay(segmentStart, segmentEnd)
			timeAdjustment = &date.TimeAdjustmentResult{
				AdjustedStartTime: segmentStart,                       // 开始时间不调整，直接使用查询时间
				AdjustedEndTime:   originalAdjustment.AdjustedEndTime, // 结束时间使用原来的调整逻辑
				NeedsFiltering:    true,
			}
		}

		// 只有当时间段有效且需要过滤时才添加
		// 对于没有结束时间的情况，不需要检查 segmentStart.Before(segmentEnd)
		if (hasEndTime && segmentStart.Before(segmentEnd) && timeAdjustment.NeedsFiltering) ||
			(!hasEndTime && timeAdjustment.NeedsFiltering) {
			segments = append(segments, TimeSegmentByStrategy{
				startDate:            timeAdjustment.AdjustedStartTime,
				endDate:              timeAdjustment.AdjustedEndTime,
				monetizationStrategy: history.MonetizationStrategy,
				needsFiltering:       true,
				hasEndTime:           hasEndTime,
			})
		}
	}

	return segments
}

// TimeSegmentByStrategy 基于广告显示状态的时间段结构
type TimeSegmentByStrategy struct {
	startDate            time.Time
	endDate              time.Time
	monetizationStrategy int  // 广告显示状态：0 显示所有广告，1 只显示游戏内广告
	needsFiltering       bool // 是否需要过滤（只显示游戏内广告）
	hasEndTime           bool // 是否有结束时间，如果为false则不添加结束时间条件
}
